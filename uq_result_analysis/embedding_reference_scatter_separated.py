#!/usr/bin/env python3
"""
为topic_labeling任务生成分离E5和Qwen模型的可视化
- X轴：UQ方法的不确定性分数（自动归一化到0-1范围）
- Y轴：到reference的距离（0-1范围）
- 颜色：区分E5和Qwen两个embedding模型

样式配置说明：
- 修改图表样式请编辑下面的 EmbeddingPlotStyle 类
- 可以调整图形大小、字体大小、颜色、透明度等所有参数
- 支持自动数据归一化和统一的坐标轴范围设置
- 使用 --no-title 参数可生成无标题版本的图表
"""
import argparse
from pathlib import Path
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

# ==================== 样式配置区域 - 在这里修改图表样式 ====================
class EmbeddingPlotStyle:
    """Embedding图表的统一样式配置"""

    def __init__(self):
        # 图形尺寸设置
        self.figure_size = (8, 8)      # 图形大小 (宽, 高) 英寸
        self.figure_dpi = 300           # 图形分辨率

        # 字体大小设置
        self.font_size_title = 16       # 标题字体大小
        self.font_size_label = 16       # 坐标轴标签字体大小
        self.font_size_tick = 16        # 刻度标签字体大小
        self.font_size_annotation = 16  # 注释字体大小

        # 散点图设置
        self.scatter_size = 30          # 散点大小
        self.scatter_alpha = 0.7        # 散点透明度 (0-1)
        self.scatter_edge_width = 0.5   # 散点边框宽度
        self.scatter_edge_color = 'white'  # 散点边框颜色

        # 线条设置
        self.line_width = 2.5           # 拟合线宽度
        self.line_alpha = 0.8           # 拟合线透明度 (0-1)

        # 颜色设置 (使用十六进制颜色代码)
        self.colors = {
            'e5': '#1f77b4',           # E5模型颜色 (蓝色)
            'qwen': '#ff7f0e',         # Qwen模型颜色 (橙色)
            'fit_line': '#d62728',     # 拟合线颜色 (红色)
        }

        # 坐标轴范围设置
        self.x_min, self.x_max = 0, 1   # X轴范围
        self.y_min, self.y_max = 0, 1   # Y轴范围

        # 网格设置
        self.grid_alpha = 0.3           # 网格透明度 (0-1)

        # 归一化设置
        self.normalize_threshold = 1.0  # 超过此值时进行归一化
        self.show_normalization_info = True  # 是否在标签中显示归一化信息

        # 注释框设置
        self.annotation_colors = {
            'formula': '#f5deb3',      # 公式框颜色 (wheat)
            'r2': '#add8e6',           # R²框颜色 (lightblue)
            'pvalue': '#f08080',       # p值框颜色 (lightcoral)
            'sample': '#90ee90'        # 样本数框颜色 (lightgreen)
        }

    def apply_global_style(self):
        """应用全局matplotlib样式设置"""
        plt.rcParams.update({
            'font.size': 18,
            'axes.titlesize': self.font_size_title,
            'axes.labelsize': self.font_size_label,
            'xtick.labelsize': self.font_size_tick,
            'ytick.labelsize': self.font_size_tick,
            'figure.figsize': self.figure_size,
            'figure.dpi': self.figure_dpi,
            'lines.linewidth': self.line_width,
            # ACM论文专用带线衬字体设置
            'font.family': 'serif',
            'font.serif': ['Times New Roman', 'Times', 'DejaVu Serif', 'Bitstream Vera Serif', 'Computer Modern Roman', 'New Century Schoolbook', 'Century Schoolbook L', 'Utopia', 'ITC Bookman', 'Bookman', 'Nimbus Roman No9 L', 'Palatino', 'Charter', 'serif'],
            'mathtext.fontset': 'cm',  # Computer Modern数学字体
            'axes.unicode_minus': False
        })

    def normalize_data(self, data):
        """数据归一化处理"""
        data_max = np.max(data)
        if data_max > self.normalize_threshold:
            normalized_data = data / data_max
            if self.show_normalization_info:
                info_suffix = f' (normalized by max={data_max:.3f})'
            else:
                info_suffix = ''
            return normalized_data, info_suffix
        else:
            return data, ''

# 创建全局样式配置实例
STYLE = EmbeddingPlotStyle()
# ==================== 样式配置区域结束 ====================

def safe_float(v):
    try:
        if pd.isna(v):
            return np.nan
        return float(v)
    except Exception:
        return np.nan

def load_task_dataframe(task: str) -> pd.DataFrame:
    csv_path = Path(f'uq_result_analysis/data/UQ_result_{task}_with_reference.csv')
    if not csv_path.exists():
        raise FileNotFoundError(f"未找到数据文件: {csv_path}")
    return pd.read_csv(csv_path)

def extract_embedding_data_separated(df: pd.DataFrame) -> pd.DataFrame:
    """分别提取E5和Qwen的reference距离数据"""
    # 只保留embedding方法
    emb_df = df[df['uq_method'].str.contains('Embedding', case=False, na=False)].copy()
    if emb_df.empty:
        raise ValueError('CSV 中未找到 Embedding 方法行。')

    # 提取embedding模型类型和reference距离
    emb_df['embedding_model'] = emb_df['uq_method'].apply(
        lambda x: 'E5' if 'E5' in x else ('Qwen' if 'Qwen' in x else 'Unknown')
    )
    emb_df['reference_distance'] = emb_df['avg_distance_to_reference'].apply(safe_float)

    # 只保留有效的reference距离
    emb_df = emb_df.dropna(subset=['reference_distance'])

    return emb_df[['document_id', 'llm_model', 'embedding_model', 'reference_distance']]

def extract_uq_scores(df: pd.DataFrame, methods: list) -> pd.DataFrame:
    """提取指定UQ方法的分数"""
    subset = df[df['uq_method'].isin(methods)].copy()
    if subset.empty:
        return pd.DataFrame({'document_id': []})
    
    # 选择分数列
    def row_score(r):
        for k in ['uncertainty_score', 'uq_value']:
            v = r.get(k)
            if not pd.isna(v):
                return v
        return np.nan
    
    subset['uq_score'] = subset.apply(row_score, axis=1)
    wide = subset.pivot_table(index='document_id', columns='uq_method', 
                             values='uq_score', aggfunc='first')
    wide.reset_index(inplace=True)
    return wide

def linear_fit(x: np.ndarray, y: np.ndarray) -> dict:
    """线性拟合，包含p-value计算"""
    mask = (~np.isnan(x)) & (~np.isnan(y))
    x_clean = x[mask]
    y_clean = y[mask]

    if len(x_clean) < 3:  # 至少需要3个点来计算p-value
        return {"coef": None, "intercept": None, "r2": None, "p_value": None, "n": len(x_clean)}

    # 使用numpy进行线性拟合
    coef = np.polyfit(x_clean, y_clean, 1)
    y_pred = np.polyval(coef, x_clean)

    # 计算R²
    ss_tot = np.sum((y_clean - np.mean(y_clean))**2)
    ss_res = np.sum((y_clean - y_pred)**2)
    r2 = 1 - (ss_res / ss_tot) if ss_tot > 0 else None

    # 计算p-value
    n = len(x_clean)
    slope = coef[0]

    # 计算标准误差
    mse = ss_res / (n - 2)  # 均方误差
    x_mean = np.mean(x_clean)
    sxx = np.sum((x_clean - x_mean)**2)
    se_slope = np.sqrt(mse / sxx)  # 斜率的标准误差

    # t统计量
    t_stat = slope / se_slope if se_slope > 0 else 0

    # 计算p-value (双尾检验)
    from scipy import stats
    p_value = 2 * (1 - stats.t.cdf(abs(t_stat), n - 2))

    return {
        "coef": float(coef[0]),
        "intercept": float(coef[1]),
        "r2": float(r2) if r2 is not None else None,
        "p_value": float(p_value),
        "n": int(len(x_clean))
    }

def main():
    # 应用样式配置
    STYLE.apply_global_style()

    parser = argparse.ArgumentParser()
    parser.add_argument('--task', default='topic_labeling', help='任务名称')
    parser.add_argument('--uq-methods', default='', help='逗号分隔的UQ方法名称，留空则使用所有非embedding方法')
    parser.add_argument('--no-title', action='store_true', help='不显示图表标题')
    args = parser.parse_args()
    
    # 加载数据
    df = load_task_dataframe(args.task)
    
    # 提取embedding数据（分离E5和Qwen）
    emb_data = extract_embedding_data_separated(df)
    print(f"提取到 {len(emb_data)} 条embedding数据")
    print(f"E5数据: {len(emb_data[emb_data['embedding_model'] == 'E5'])} 条")
    print(f"Qwen数据: {len(emb_data[emb_data['embedding_model'] == 'Qwen'])} 条")
    
    # 获取UQ方法列表（排除embedding方法）
    if args.uq_methods.strip():
        uq_methods = [m.strip() for m in args.uq_methods.split(',') if m.strip()]
    else:
        all_methods = df['uq_method'].unique()
        # uq_methods = [m for m in all_methods if 'Embedding' not in m]
        uq_methods = [m for m in all_methods]
    
    print(f"将分析以下UQ方法: {uq_methods}")
    
    # 提取UQ分数
    uq_scores = extract_uq_scores(df, uq_methods)
    
    # 创建输出目录
    fig_dir = Path('uq_result_analysis/figures')
    fig_dir.mkdir(parents=True, exist_ok=True)

    # 创建专门的子目录
    task_fig_dir = fig_dir / f'{args.task}_embedding_reference'
    task_fig_dir.mkdir(parents=True, exist_ok=True)
    
    # 获取LLM模型列表
    llm_models = df['llm_model'].unique()
    print(f"发现LLM模型: {llm_models}")

    # 为每个LLM模型和UQ方法组合生成图表
    fit_results = {}

    for llm_model in llm_models:
        fit_results[llm_model] = {}

        # 过滤当前LLM模型的数据
        llm_emb_data = emb_data[emb_data['llm_model'] == llm_model]

        for method in uq_methods:
            if method not in uq_scores.columns:
                print(f"跳过方法 {method}: 数据中不存在")
                continue

            # 合并数据
            merged = llm_emb_data.merge(uq_scores[['document_id', method]], on='document_id', how='inner')
            merged = merged.dropna(subset=[method, 'reference_distance'])

            if len(merged) < 2:
                print(f"跳过 {llm_model} - {method}: 有效数据点不足 ({len(merged)})")
                continue

            # 分离E5和Qwen数据
            e5_data = merged[merged['embedding_model'] == 'E5']
            qwen_data = merged[merged['embedding_model'] == 'Qwen']

            fit_results[llm_model][method] = {}

            # 为E5创建独立图表
            if len(e5_data) > 1:
                plt.figure(figsize=STYLE.figure_size)

                x_e5 = e5_data[method].values
                y_e5 = e5_data['reference_distance'].values

                # 使用样式配置进行数据归一化
                x_e5_normalized, x_label_suffix = STYLE.normalize_data(x_e5)

                plt.scatter(x_e5_normalized, y_e5,
                           s=STYLE.scatter_size,
                           alpha=STYLE.scatter_alpha,
                           color=STYLE.colors['e5'],
                           edgecolors=STYLE.scatter_edge_color,
                           linewidth=STYLE.scatter_edge_width)

                # E5拟合（使用归一化后的数据）
                fit_e5 = linear_fit(x_e5_normalized, y_e5)
                fit_results[llm_model][method]['E5'] = fit_e5

                if fit_e5['coef'] is not None:
                    x_range = np.linspace(np.min(x_e5_normalized), np.max(x_e5_normalized), 100)
                    y_fit = fit_e5['coef'] * x_range + fit_e5['intercept']
                    plt.plot(x_range, y_fit,
                            color=STYLE.colors['fit_line'],
                            linestyle='-',
                            linewidth=STYLE.line_width,
                            alpha=STYLE.line_alpha)

                    # 添加拟合信息
                    coef, intercept, r2, p_value, n = fit_e5['coef'], fit_e5['intercept'], fit_e5['r2'], fit_e5['p_value'], fit_e5['n']
                    if intercept >= 0:
                        formula = f'y = {coef:.4f}x + {intercept:.4f}'
                    else:
                        formula = f'y = {coef:.4f}x - {abs(intercept):.4f}'
                    r2_str = f"R² = {r2:.4f}" if r2 is not None else "R² = -"
                    p_str = f"p = {p_value:.4f}" if p_value is not None else "p = -"

                    plt.text(0.05, 0.95, formula, transform=plt.gca().transAxes,
                            fontsize=12, verticalalignment='top',
                            bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
                    plt.text(0.05, 0.88, r2_str, transform=plt.gca().transAxes,
                            fontsize=12, verticalalignment='top',
                            bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
                    plt.text(0.05, 0.81, p_str, transform=plt.gca().transAxes,
                            fontsize=12, verticalalignment='top',
                            bbox=dict(boxstyle='round', facecolor='lightcoral', alpha=0.8))
                    plt.text(0.05, 0.74, f'n = {n}', transform=plt.gca().transAxes,
                            fontsize=12, verticalalignment='top',
                            bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.8))

                plt.xlabel(f'{method} Score{x_label_suffix}', fontsize=STYLE.font_size_label)
                plt.ylabel('Distance to Reference Text (E5)', fontsize=STYLE.font_size_label)

                # 根据参数决定是否显示标题
                if not args.no_title:
                    plt.title(f'{args.task}: {llm_model} - {method} vs Reference Distance (E5)', fontsize=STYLE.font_size_title)

                # 设置坐标轴范围（使用样式配置）
                plt.xlim(STYLE.x_min, STYLE.x_max)
                plt.ylim(STYLE.y_min, STYLE.y_max)

                plt.grid(True, alpha=STYLE.grid_alpha)
                plt.tight_layout()

                # 保存E5图像 (PNG和PDF)
                safe_llm_name = llm_model.replace(':', '_').replace('/', '_')
                title_suffix = '_notitle' if args.no_title else ''
                png_file_e5 = task_fig_dir / f'{safe_llm_name}_{method}_E5{title_suffix}.png'
                pdf_file_e5 = task_fig_dir / f'{safe_llm_name}_{method}_E5{title_suffix}.pdf'
                plt.savefig(png_file_e5, dpi=STYLE.figure_dpi, bbox_inches='tight')
                plt.savefig(pdf_file_e5, bbox_inches='tight')
                plt.close()
                print(f'保存 {llm_model} - {method} E5图像到 {png_file_e5} 和 {pdf_file_e5}')

            # 为Qwen创建独立图表
            if len(qwen_data) > 1:
                plt.figure(figsize=STYLE.figure_size)

                x_qwen = qwen_data[method].values
                y_qwen = qwen_data['reference_distance'].values

                # 使用样式配置进行数据归一化
                x_qwen_normalized, x_label_suffix_qwen = STYLE.normalize_data(x_qwen)

                plt.scatter(x_qwen_normalized, y_qwen,
                           s=STYLE.scatter_size,
                           alpha=STYLE.scatter_alpha,
                           color=STYLE.colors['qwen'],
                           edgecolors=STYLE.scatter_edge_color,
                           linewidth=STYLE.scatter_edge_width)

                # Qwen拟合（使用归一化后的数据）
                fit_qwen = linear_fit(x_qwen_normalized, y_qwen)
                fit_results[llm_model][method]['Qwen'] = fit_qwen

                if fit_qwen['coef'] is not None:
                    x_range = np.linspace(np.min(x_qwen_normalized), np.max(x_qwen_normalized), 100)
                    y_fit = fit_qwen['coef'] * x_range + fit_qwen['intercept']
                    plt.plot(x_range, y_fit,
                            color=STYLE.colors['fit_line'],
                            linestyle='-',
                            linewidth=STYLE.line_width,
                            alpha=STYLE.line_alpha)

                    # 添加拟合信息
                    coef, intercept, r2, p_value, n = fit_qwen['coef'], fit_qwen['intercept'], fit_qwen['r2'], fit_qwen['p_value'], fit_qwen['n']
                    if intercept >= 0:
                        formula = f'y = {coef:.4f}x + {intercept:.4f}'
                    else:
                        formula = f'y = {coef:.4f}x - {abs(intercept):.4f}'
                    r2_str = f"R² = {r2:.4f}" if r2 is not None else "R² = -"
                    p_str = f"p = {p_value:.4f}" if p_value is not None else "p = -"

                    plt.text(0.05, 0.95, formula, transform=plt.gca().transAxes,
                            fontsize=12, verticalalignment='top',
                            bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
                    plt.text(0.05, 0.88, r2_str, transform=plt.gca().transAxes,
                            fontsize=12, verticalalignment='top',
                            bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
                    plt.text(0.05, 0.81, p_str, transform=plt.gca().transAxes,
                            fontsize=12, verticalalignment='top',
                            bbox=dict(boxstyle='round', facecolor='lightcoral', alpha=0.8))
                    plt.text(0.05, 0.74, f'n = {n}', transform=plt.gca().transAxes,
                            fontsize=12, verticalalignment='top',
                            bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.8))

                plt.xlabel(f'{method} Score{x_label_suffix_qwen}', fontsize=STYLE.font_size_label)
                plt.ylabel('Distance to Reference Text (Qwen)', fontsize=STYLE.font_size_label)

                # 根据参数决定是否显示标题
                if not args.no_title:
                    plt.title(f'{args.task}: {llm_model} - {method} vs Reference Distance (Qwen)', fontsize=STYLE.font_size_title)

                # 设置坐标轴范围（使用样式配置）
                plt.xlim(STYLE.x_min, STYLE.x_max)
                plt.ylim(STYLE.y_min, STYLE.y_max)

                plt.grid(True, alpha=STYLE.grid_alpha)
                plt.tight_layout()

                # 保存Qwen图像 (PNG和PDF)
                safe_llm_name = llm_model.replace(':', '_').replace('/', '_')
                title_suffix = '_notitle' if args.no_title else ''
                png_file_qwen = task_fig_dir / f'{safe_llm_name}_{method}_Qwen{title_suffix}.png'
                pdf_file_qwen = task_fig_dir / f'{safe_llm_name}_{method}_Qwen{title_suffix}.pdf'
                plt.savefig(png_file_qwen, dpi=STYLE.figure_dpi, bbox_inches='tight')
                plt.savefig(pdf_file_qwen, bbox_inches='tight')
                plt.close()
                print(f'保存 {llm_model} - {method} Qwen图像到 {png_file_qwen} 和 {pdf_file_qwen}')
    
    # 保存拟合结果为CSV格式
    fit_csv = task_fig_dir / 'fitting_results.csv'

    # 将嵌套字典转换为DataFrame
    rows = []
    for llm_model in fit_results:
        for method in fit_results[llm_model]:
            for emb_model in fit_results[llm_model][method]:
                data = fit_results[llm_model][method][emb_model]
                row = {
                    'task': args.task,
                    'llm_model': llm_model,
                    'uq_method': method,
                    'embedding_model': emb_model,
                    'coefficient': data['coef'],
                    'intercept': data['intercept'],
                    'r_squared': data['r2'],
                    'p_value': data['p_value'],
                    'sample_size': data['n']
                }
                rows.append(row)

    # 创建DataFrame并保存为CSV
    import pandas as pd
    df_results = pd.DataFrame(rows)
    df_results.to_csv(fit_csv, index=False)

    print(f'拟合参数保存到 {fit_csv}')
    print(f'共生成 {len(fit_results)} 个分离图表，保存在 {task_fig_dir}')
    print(f'每个UQ方法生成了E5和Qwen两个图表，格式包括PNG和PDF')

if __name__ == '__main__':
    main()
