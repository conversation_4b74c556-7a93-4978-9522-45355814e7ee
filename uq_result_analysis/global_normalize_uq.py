#!/usr/bin/env python3
"""
对combined_uq_results.csv中的UQ数据进行全局归一化处理
为每个UQ方法计算全局Min-Max归一化，并添加归一化后的列到CSV中
"""

import pandas as pd
import numpy as np
from pathlib import Path
import argparse

def safe_float(v):
    """安全转换为浮点数"""
    try:
        if pd.isna(v):
            return np.nan
        return float(v)
    except Exception:
        return np.nan

def compute_global_normalization_params(df: pd.DataFrame) -> dict:
    """计算每个UQ方法的全局归一化参数"""
    print("计算全局归一化参数...")
    global_params = {}
    
    # 获取所有唯一的UQ方法
    uq_methods = df['uq_method'].unique()
    print(f"发现 {len(uq_methods)} 个UQ方法")
    
    for method in uq_methods:
        # 提取该UQ方法的所有数据
        method_data = df[df['uq_method'] == method].copy()
        
        # 获取UQ分数 (优先使用uncertainty_score，其次uq_value)
        uq_scores = []
        for _, row in method_data.iterrows():
            score = safe_float(row.get('uncertainty_score'))
            if pd.isna(score):
                score = safe_float(row.get('uq_value'))
            if not pd.isna(score):
                uq_scores.append(score)
        
        if len(uq_scores) > 0:
            uq_scores = np.array(uq_scores)
            global_min = float(np.min(uq_scores))
            global_max = float(np.max(uq_scores))
            global_mean = float(np.mean(uq_scores))
            global_std = float(np.std(uq_scores))
            
            global_params[method] = {
                'min': global_min,
                'max': global_max,
                'mean': global_mean,
                'std': global_std,
                'count': len(uq_scores),
                'range': global_max - global_min
            }
            
            print(f"  {method}:")
            print(f"    范围: [{global_min:.4f}, {global_max:.4f}] (range={global_max-global_min:.4f})")
            print(f"    均值±标准差: {global_mean:.4f}±{global_std:.4f}")
            print(f"    样本数: {len(uq_scores)}")
        else:
            print(f"  {method}: 无有效数据")
    
    return global_params

def apply_global_normalization(df: pd.DataFrame, global_params: dict) -> pd.DataFrame:
    """应用全局归一化到数据框"""
    print("\n应用全局归一化...")
    df_normalized = df.copy()
    
    # 添加归一化列
    df_normalized['uq_score_normalized'] = np.nan
    df_normalized['uq_score_zscore'] = np.nan
    df_normalized['normalization_method'] = ''
    df_normalized['global_min'] = np.nan
    df_normalized['global_max'] = np.nan
    df_normalized['global_range'] = np.nan
    
    for method in global_params:
        method_mask = df_normalized['uq_method'] == method
        method_data = df_normalized[method_mask].copy()
        
        if len(method_data) == 0:
            continue
            
        params = global_params[method]
        global_min = params['min']
        global_max = params['max']
        global_mean = params['mean']
        global_std = params['std']
        global_range = params['range']
        
        # 获取原始UQ分数
        original_scores = []
        for idx, row in method_data.iterrows():
            score = safe_float(row.get('uncertainty_score'))
            if pd.isna(score):
                score = safe_float(row.get('uq_value'))
            original_scores.append(score)
        
        original_scores = np.array(original_scores)
        
        # Min-Max归一化到[0,1]
        if global_range > 0:
            normalized_scores = (original_scores - global_min) / global_range
        else:
            # 如果所有值都相同，设为0.5
            normalized_scores = np.full_like(original_scores, 0.5)
        
        # Z-score标准化
        if global_std > 0:
            zscore_scores = (original_scores - global_mean) / global_std
        else:
            zscore_scores = np.zeros_like(original_scores)
        
        # 更新数据框
        method_indices = method_data.index
        df_normalized.loc[method_indices, 'uq_score_normalized'] = normalized_scores
        df_normalized.loc[method_indices, 'uq_score_zscore'] = zscore_scores
        df_normalized.loc[method_indices, 'normalization_method'] = 'min_max_global'
        df_normalized.loc[method_indices, 'global_min'] = global_min
        df_normalized.loc[method_indices, 'global_max'] = global_max
        df_normalized.loc[method_indices, 'global_range'] = global_range
        
        print(f"  {method}: 归一化 {len(method_data)} 条记录")
    
    return df_normalized

def generate_normalization_summary(global_params: dict, output_dir: Path):
    """生成归一化参数摘要文件"""
    summary_data = []
    
    for method, params in global_params.items():
        summary_data.append({
            'uq_method': method,
            'global_min': params['min'],
            'global_max': params['max'],
            'global_mean': params['mean'],
            'global_std': params['std'],
            'global_range': params['range'],
            'sample_count': params['count']
        })
    
    summary_df = pd.DataFrame(summary_data)
    summary_df = summary_df.sort_values('uq_method')
    
    summary_file = output_dir / 'global_normalization_summary.csv'
    summary_df.to_csv(summary_file, index=False)
    print(f"\n归一化参数摘要保存到: {summary_file}")
    
    return summary_df

def main():
    parser = argparse.ArgumentParser(description='对UQ数据进行全局归一化处理')
    parser.add_argument('--input', default='uq_result_analysis/data/combined_uq_results.csv', 
                       help='输入CSV文件路径')
    parser.add_argument('--output', default='uq_result_analysis/data/combined_uq_results_normalized.csv',
                       help='输出CSV文件路径')
    parser.add_argument('--summary-dir', default='uq_result_analysis/data',
                       help='归一化摘要文件输出目录')
    args = parser.parse_args()
    
    # 检查输入文件
    input_path = Path(args.input)
    if not input_path.exists():
        print(f"错误: 输入文件不存在: {input_path}")
        return
    
    # 创建输出目录
    output_path = Path(args.output)
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    summary_dir = Path(args.summary_dir)
    summary_dir.mkdir(parents=True, exist_ok=True)
    
    print(f"读取数据文件: {input_path}")
    df = pd.read_csv(input_path)
    print(f"原始数据: {len(df)} 行, {len(df.columns)} 列")
    
    # 只处理成功的记录
    success_df = df[df['status'] == 'success'].copy()
    print(f"成功记录: {len(success_df)} 行")
    
    # 计算全局归一化参数
    global_params = compute_global_normalization_params(success_df)
    
    # 应用全局归一化
    normalized_df = apply_global_normalization(df, global_params)
    
    # 生成归一化摘要
    summary_df = generate_normalization_summary(global_params, summary_dir)
    
    # 保存结果
    print(f"\n保存归一化后的数据到: {output_path}")
    normalized_df.to_csv(output_path, index=False)
    
    print(f"\n处理完成!")
    print(f"- 原始数据: {len(df)} 行")
    print(f"- 成功记录: {len(success_df)} 行")
    print(f"- 处理的UQ方法: {len(global_params)} 个")
    print(f"- 输出文件: {output_path}")
    print(f"- 摘要文件: {summary_dir / 'global_normalization_summary.csv'}")
    
    # 显示归一化后的数据统计
    print(f"\n归一化后的数据统计:")
    normalized_valid = normalized_df.dropna(subset=['uq_score_normalized'])
    print(f"- 有效归一化记录: {len(normalized_valid)} 行")
    print(f"- 归一化值范围: [{normalized_valid['uq_score_normalized'].min():.4f}, {normalized_valid['uq_score_normalized'].max():.4f}]")

if __name__ == '__main__':
    main()
