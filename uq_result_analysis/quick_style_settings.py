#!/usr/bin/env python3
"""
快速样式设置文件
在这里修改常用的样式参数，然后运行脚本应用更改
"""

from plot_style_config import get_style_config

def apply_custom_style():
    """应用自定义样式设置"""
    
    style = get_style_config()
    
    # ==================== 在这里修改你的样式参数 ====================
    
    # 图形大小设置 (宽, 高) 英寸
    style.figure_size = (10, 8)
    style.figure_dpi = 300
    
    # 字体大小设置
    style.font_size_base = 14        # 基础字体大小
    style.font_size_title = 16       # 标题字体大小
    style.font_size_label = 14       # 坐标轴标签字体大小
    style.font_size_tick = 12        # 刻度标签字体大小
    style.font_size_legend = 12      # 图例字体大小
    style.font_size_annotation = 10  # 注释字体大小
    
    # 散点图设置
    style.scatter_size = 30          # 散点大小
    style.scatter_alpha = 0.7        # 散点透明度 (0-1)
    style.scatter_edge_width = 0.5   # 散点边框宽度
    
    # 线条设置
    style.line_width = 2.5           # 拟合线宽度
    style.line_alpha = 0.8           # 拟合线透明度 (0-1)
    
    # 颜色设置 (使用十六进制颜色代码)
    style.colors['e5'] = '#1f77b4'           # E5模型颜色 (蓝色)
    style.colors['qwen'] = '#ff7f0e'         # Qwen模型颜色 (橙色)
    style.colors['fit_line'] = '#d62728'     # 拟合线颜色 (红色)
    style.colors['baseline'] = '#7f7f7f'     # 基准线颜色 (灰色)
    
    # 坐标轴范围设置
    style.axis_limits['x_min'] = 0     # X轴最小值
    style.axis_limits['x_max'] = 1     # X轴最大值
    style.axis_limits['y_min'] = 0     # Y轴最小值
    style.axis_limits['y_max'] = 1     # Y轴最大值
    
    # 网格设置
    style.grid_enabled = True          # 是否显示网格
    style.grid_alpha = 0.3            # 网格透明度 (0-1)
    style.grid_line_width = 0.5       # 网格线宽
    
    # 图例设置
    style.legend_frameon = False       # 图例是否显示边框
    style.legend_ncol = 1             # 图例列数
    style.legend_loc = 'best'         # 图例位置
    
    # 注释框颜色设置
    style.annotation_colors['formula'] = '#f5deb3'    # 公式框颜色 (wheat)
    style.annotation_colors['r2'] = '#add8e6'         # R²框颜色 (lightblue)
    style.annotation_colors['pvalue'] = '#f08080'     # p值框颜色 (lightcoral)
    style.annotation_colors['sample'] = '#90ee90'     # 样本数框颜色 (lightgreen)
    
    # 归一化设置
    style.normalize_threshold = 1.0    # 超过此值时进行归一化
    style.show_normalization_info = True  # 是否在标签中显示归一化信息
    
    # 文件保存设置
    style.save_formats = ['png', 'pdf']  # 保存格式列表
    
    print("✅ 自定义样式设置已应用！")
    return style

def print_current_settings():
    """打印当前样式设置"""
    style = get_style_config()
    
    print("📊 当前样式设置:")
    print("=" * 50)
    print(f"图形大小: {style.figure_size} (宽x高 英寸)")
    print(f"图形分辨率: {style.figure_dpi} DPI")
    print()
    print("字体大小:")
    print(f"  - 基础字体: {style.font_size_base}")
    print(f"  - 标题字体: {style.font_size_title}")
    print(f"  - 坐标轴标签: {style.font_size_label}")
    print(f"  - 图例字体: {style.font_size_legend}")
    print()
    print("散点图设置:")
    print(f"  - 点大小: {style.scatter_size}")
    print(f"  - 透明度: {style.scatter_alpha}")
    print(f"  - 边框宽度: {style.scatter_edge_width}")
    print()
    print("线条设置:")
    print(f"  - 线宽: {style.line_width}")
    print(f"  - 透明度: {style.line_alpha}")
    print()
    print("颜色设置:")
    print(f"  - E5模型: {style.colors['e5']}")
    print(f"  - Qwen模型: {style.colors['qwen']}")
    print(f"  - 拟合线: {style.colors['fit_line']}")
    print()
    print("坐标轴范围:")
    print(f"  - X轴: [{style.axis_limits['x_min']}, {style.axis_limits['x_max']}]")
    print(f"  - Y轴: [{style.axis_limits['y_min']}, {style.axis_limits['y_max']}]")
    print()
    print("其他设置:")
    print(f"  - 显示网格: {style.grid_enabled}")
    print(f"  - 网格透明度: {style.grid_alpha}")
    print(f"  - 归一化阈值: {style.normalize_threshold}")
    print(f"  - 保存格式: {style.save_formats}")

def create_style_presets():
    """创建一些预设样式"""
    
    def apply_paper_style():
        """学术论文样式"""
        style = get_style_config()
        style.figure_size = (8, 6)
        style.figure_dpi = 300
        style.font_size_base = 12
        style.font_size_title = 14
        style.font_size_label = 12
        style.font_size_legend = 10
        style.scatter_size = 25
        style.line_width = 2.0
        style.colors['e5'] = '#2E86AB'
        style.colors['qwen'] = '#A23B72'
        style.colors['fit_line'] = '#F18F01'
        print("📄 已应用学术论文样式")
        return style
    
    def apply_presentation_style():
        """演示文稿样式"""
        style = get_style_config()
        style.figure_size = (12, 9)
        style.figure_dpi = 150
        style.font_size_base = 16
        style.font_size_title = 20
        style.font_size_label = 16
        style.font_size_legend = 14
        style.scatter_size = 40
        style.line_width = 3.0
        style.colors['e5'] = '#1B9E77'
        style.colors['qwen'] = '#D95F02'
        style.colors['fit_line'] = '#7570B3'
        print("🎯 已应用演示文稿样式")
        return style
    
    def apply_minimal_style():
        """简约样式"""
        style = get_style_config()
        style.figure_size = (8, 6)
        style.figure_dpi = 200
        style.font_size_base = 11
        style.font_size_title = 13
        style.font_size_label = 11
        style.font_size_legend = 9
        style.scatter_size = 20
        style.line_width = 1.5
        style.grid_alpha = 0.2
        style.colors['e5'] = '#333333'
        style.colors['qwen'] = '#666666'
        style.colors['fit_line'] = '#999999'
        print("⚪ 已应用简约样式")
        return style
    
    return {
        'paper': apply_paper_style,
        'presentation': apply_presentation_style,
        'minimal': apply_minimal_style
    }

if __name__ == '__main__':
    print("🎨 UQ分析图表样式设置")
    print("=" * 50)
    
    # 显示当前设置
    print_current_settings()
    
    print("\n" + "=" * 50)
    print("可用的预设样式:")
    print("1. paper - 学术论文样式")
    print("2. presentation - 演示文稿样式") 
    print("3. minimal - 简约样式")
    print("4. custom - 应用上面定义的自定义样式")
    
    choice = input("\n请选择样式 (1-4) 或按Enter使用当前样式: ").strip()
    
    presets = create_style_presets()
    
    if choice == '1':
        presets['paper']()
    elif choice == '2':
        presets['presentation']()
    elif choice == '3':
        presets['minimal']()
    elif choice == '4':
        apply_custom_style()
    else:
        print("使用当前样式设置")
    
    print("\n" + "=" * 50)
    print("样式设置完成！")
    print("现在可以运行绘图脚本:")
    print("python embedding_reference_scatter_separated_styled.py --task topic_labeling")
    print("python embedding_reference_scatter_separated_styled.py --task sentiment_analysis")
