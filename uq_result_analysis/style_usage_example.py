#!/usr/bin/env python3
"""
样式配置使用示例
展示如何在绘图脚本中使用统一的样式配置
"""

import numpy as np
import matplotlib.pyplot as plt
from plot_style_config import get_style_config, apply_global_style

def example_scatter_plot():
    """示例：如何使用样式配置创建散点图"""
    
    # 获取样式配置
    style = get_style_config()
    
    # 应用全局样式
    apply_global_style()
    
    # 创建示例数据
    np.random.seed(42)
    x_data = np.random.rand(100)
    y_data = 0.5 * x_data + 0.2 * np.random.rand(100)
    
    # 数据归一化（如果需要）
    x_normalized, x_suffix = style.normalize_data(x_data)
    y_normalized, y_suffix = style.normalize_data(y_data)
    
    # 创建图形
    fig = style.create_figure()
    ax = plt.gca()
    
    # 绘制散点图
    style.scatter_plot(x_normalized, y_normalized, 
                      color_key='e5', 
                      label='E5 Data',
                      ax=ax)
    
    # 添加拟合线
    fit_x = np.linspace(0, 1, 100)
    fit_y = 0.5 * fit_x + 0.1
    style.fit_line_plot(fit_x, fit_y, color_key='fit_line', ax=ax)
    
    # 设置坐标轴
    style.setup_axes(ax, 
                    xlabel=f'X Values{x_suffix}',
                    ylabel=f'Y Values{y_suffix}',
                    title='Example Scatter Plot with Unified Style')
    
    # 添加注释框
    style.add_annotation_box('R² = 0.85', (0.05, 0.95), 'r2', ax)
    style.add_annotation_box('p < 0.001', (0.05, 0.88), 'pvalue', ax)
    style.add_annotation_box('n = 100', (0.05, 0.81), 'sample', ax)
    
    # 设置图例
    style.setup_legend(ax)
    
    plt.tight_layout()
    
    # 保存图形
    saved_files = style.save_figure(fig, 'example_plot')
    print(f"图形已保存到: {saved_files}")
    
    plt.show()

def modify_style_example():
    """示例：如何修改样式配置"""
    
    style = get_style_config()
    
    # 修改样式参数
    print("原始图形大小:", style.figure_size)
    style.figure_size = (12, 9)  # 修改图形大小
    print("新的图形大小:", style.figure_size)
    
    print("原始散点大小:", style.scatter_size)
    style.scatter_size = 50  # 修改散点大小
    print("新的散点大小:", style.scatter_size)
    
    print("原始字体大小:", style.font_size_base)
    style.font_size_base = 16  # 修改基础字体大小
    print("新的字体大小:", style.font_size_base)
    
    # 修改颜色
    print("原始E5颜色:", style.colors['e5'])
    style.colors['e5'] = '#2ca02c'  # 改为绿色
    print("新的E5颜色:", style.colors['e5'])
    
    # 应用修改后的样式
    apply_global_style()
    
    print("样式修改完成！")

if __name__ == '__main__':
    print("=== 样式配置使用示例 ===")
    
    print("\n1. 创建示例散点图...")
    example_scatter_plot()
    
    print("\n2. 修改样式配置示例...")
    modify_style_example()
    
    print("\n=== 常用样式修改参数 ===")
    style = get_style_config()
    
    print(f"""
    图形尺寸设置:
    - figure_size: {style.figure_size} (宽, 高)
    - figure_dpi: {style.figure_dpi}
    
    字体大小设置:
    - font_size_base: {style.font_size_base} (基础字体)
    - font_size_title: {style.font_size_title} (标题字体)
    - font_size_label: {style.font_size_label} (坐标轴标签)
    - font_size_legend: {style.font_size_legend} (图例字体)
    
    散点图设置:
    - scatter_size: {style.scatter_size} (点大小)
    - scatter_alpha: {style.scatter_alpha} (透明度)
    
    线条设置:
    - line_width: {style.line_width} (线宽)
    - line_alpha: {style.line_alpha} (透明度)
    
    坐标轴范围:
    - x_min: {style.axis_limits['x_min']}, x_max: {style.axis_limits['x_max']}
    - y_min: {style.axis_limits['y_min']}, y_max: {style.axis_limits['y_max']}
    
    颜色设置:
    - E5颜色: {style.colors['e5']}
    - Qwen颜色: {style.colors['qwen']}
    - 拟合线颜色: {style.colors['fit_line']}
    """)
