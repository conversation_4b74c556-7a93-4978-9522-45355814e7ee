# Embedding图表样式配置说明

## 📊 概述

`embedding_reference_scatter_separated.py` 文件已经集成了统一的样式配置系统。所有的图表样式参数都集中在文件顶部的 `EmbeddingPlotStyle` 类中，你可以通过修改这个类来调整所有图表的外观。

## 🎨 如何修改样式

### 1. 打开文件
编辑 `uq_result_analysis/embedding_reference_scatter_separated.py` 文件

### 2. 找到样式配置区域
在文件的第18-100行左右，找到以下区域：
```python
# ==================== 样式配置区域 - 在这里修改图表样式 ====================
class EmbeddingPlotStyle:
    """Embedding图表的统一样式配置"""
    
    def __init__(self):
        # 在这里修改各种样式参数
```

### 3. 修改你想要的参数

## 🛠️ 主要样式参数

### 图形尺寸设置
```python
self.figure_size = (10, 8)      # 图形大小 (宽, 高) 英寸
self.figure_dpi = 300           # 图形分辨率
```

### 字体大小设置
```python
self.font_size_title = 16       # 标题字体大小
self.font_size_label = 14       # 坐标轴标签字体大小
self.font_size_tick = 12        # 刻度标签字体大小
self.font_size_annotation = 10  # 注释字体大小
```

### 散点图设置
```python
self.scatter_size = 30          # 散点大小
self.scatter_alpha = 0.7        # 散点透明度 (0-1)
self.scatter_edge_width = 0.5   # 散点边框宽度
self.scatter_edge_color = 'white'  # 散点边框颜色
```

### 线条设置
```python
self.line_width = 2.5           # 拟合线宽度
self.line_alpha = 0.8           # 拟合线透明度 (0-1)
```

### 颜色设置
```python
self.colors = {
    'e5': '#1f77b4',           # E5模型颜色 (蓝色)
    'qwen': '#ff7f0e',         # Qwen模型颜色 (橙色)
    'fit_line': '#d62728',     # 拟合线颜色 (红色)
}
```

### 坐标轴范围设置
```python
self.x_min, self.x_max = 0, 1   # X轴范围
self.y_min, self.y_max = 0, 1   # Y轴范围
```

### 网格设置
```python
self.grid_alpha = 0.3           # 网格透明度 (0-1)
```

### 归一化设置
```python
self.normalize_threshold = 1.0  # 超过此值时进行归一化
self.show_normalization_info = True  # 是否在标签中显示归一化信息
```

### 注释框颜色设置
```python
self.annotation_colors = {
    'formula': '#f5deb3',      # 公式框颜色 (wheat)
    'r2': '#add8e6',           # R²框颜色 (lightblue)
    'pvalue': '#f08080',       # p值框颜色 (lightcoral)
    'sample': '#90ee90'        # 样本数框颜色 (lightgreen)
}
```

## 🎯 常用修改示例

### 示例1：制作更大的演示文稿图表
```python
self.figure_size = (12, 9)      # 更大的图形
self.font_size_title = 20       # 更大的标题
self.font_size_label = 16       # 更大的标签
self.scatter_size = 40          # 更大的散点
self.line_width = 3.0           # 更粗的线条
```

### 示例2：制作学术论文风格
```python
self.figure_size = (8, 6)       # 标准学术尺寸
self.figure_dpi = 300           # 高分辨率
self.font_size_title = 14       # 适中的标题
self.font_size_label = 12       # 适中的标签
self.scatter_size = 25          # 适中的散点
self.colors['e5'] = '#2E86AB'   # 学术蓝色
self.colors['qwen'] = '#A23B72' # 学术紫色
```

### 示例3：制作简约黑白风格
```python
self.colors['e5'] = '#333333'       # 深灰色
self.colors['qwen'] = '#666666'     # 中灰色
self.colors['fit_line'] = '#999999' # 浅灰色
self.grid_alpha = 0.2               # 更淡的网格
self.scatter_size = 20              # 更小的散点
```

## 🚀 使用方法

1. 修改样式参数后，保存文件
2. 运行脚本生成图表：
   ```bash
   python uq_result_analysis/embedding_reference_scatter_separated.py --task topic_labeling
   python uq_result_analysis/embedding_reference_scatter_separated.py --task sentiment_analysis
   ```
3. 图表会自动应用新的样式设置

## 🎨 颜色代码参考

### 常用颜色十六进制代码：
- **蓝色系**: `#1f77b4` (默认蓝), `#2E86AB` (学术蓝), `#4472C4` (Office蓝)
- **橙色系**: `#ff7f0e` (默认橙), `#F18F01` (学术橙), `#E36C09` (深橙)
- **红色系**: `#d62728` (默认红), `#C5282F` (学术红), `#A23B72` (紫红)
- **绿色系**: `#2ca02c` (默认绿), `#1B9E77` (学术绿), `#70AD47` (Office绿)
- **灰色系**: `#333333` (深灰), `#666666` (中灰), `#999999` (浅灰)

## 📝 注意事项

- 修改后需要重新运行脚本才能看到效果
- 图形大小建议保持宽高比在4:3到16:9之间
- 透明度值范围是0-1，0为完全透明，1为完全不透明
- DPI建议设置为150-300之间，过高会导致文件过大
- 字体大小建议保持标题>标签>刻度的层次关系
