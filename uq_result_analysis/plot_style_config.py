#!/usr/bin/env python3
"""
统一的绘图样式配置文件
用于控制所有UQ分析图表的视觉样式
"""

import matplotlib.pyplot as plt
import numpy as np

class PlotStyleConfig:
    """统一的绘图样式配置类"""
    
    def __init__(self):
        # ==================== 图形尺寸设置 ====================
        self.figure_size = (10, 8)  # 图形大小 (宽, 高) 英寸
        self.figure_dpi = 300       # 图形分辨率
        
        # ==================== 字体设置 ====================
        self.font_size_base = 14        # 基础字体大小
        self.font_size_title = 16       # 标题字体大小
        self.font_size_label = 14       # 坐标轴标签字体大小
        self.font_size_tick = 12        # 刻度标签字体大小
        self.font_size_legend = 12      # 图例字体大小
        self.font_size_annotation = 10  # 注释字体大小
        
        # ==================== 散点图设置 ====================
        self.scatter_size = 30          # 散点大小
        self.scatter_alpha = 0.7        # 散点透明度
        self.scatter_edge_width = 0.5   # 散点边框宽度
        self.scatter_edge_color = 'white'  # 散点边框颜色
        
        # ==================== 线条设置 ====================
        self.line_width = 2.5           # 拟合线宽度
        self.line_alpha = 0.8           # 拟合线透明度
        self.line_style_fit = '-'       # 拟合线样式
        self.line_style_baseline = '--' # 基准线样式
        
        # ==================== 颜色设置 ====================
        self.colors = {
            'e5': '#1f77b4',           # E5模型颜色 (蓝色)
            'qwen': '#ff7f0e',         # Qwen模型颜色 (橙色)
            'fit_line': '#d62728',     # 拟合线颜色 (红色)
            'baseline': '#7f7f7f',     # 基准线颜色 (灰色)
            'grid': '#cccccc'          # 网格颜色
        }
        
        # ==================== 坐标轴设置 ====================
        self.axis_limits = {
            'x_min': 0,     # X轴最小值
            'x_max': 1,     # X轴最大值
            'y_min': 0,     # Y轴最小值
            'y_max': 1      # Y轴最大值
        }
        
        # ==================== 网格设置 ====================
        self.grid_enabled = True        # 是否显示网格
        self.grid_alpha = 0.3          # 网格透明度
        self.grid_line_width = 0.5     # 网格线宽
        
        # ==================== 图例设置 ====================
        self.legend_frameon = False     # 图例是否显示边框
        self.legend_ncol = 1           # 图例列数
        self.legend_loc = 'best'       # 图例位置
        
        # ==================== 注释框设置 ====================
        self.annotation_box_style = 'round'  # 注释框样式
        self.annotation_box_alpha = 0.8      # 注释框透明度
        self.annotation_colors = {
            'formula': '#f5deb3',      # 公式框颜色 (wheat)
            'r2': '#add8e6',           # R²框颜色 (lightblue)
            'pvalue': '#f08080',       # p值框颜色 (lightcoral)
            'sample': '#90ee90'        # 样本数框颜色 (lightgreen)
        }
        
        # ==================== 归一化设置 ====================
        self.normalize_threshold = 1.0  # 超过此值时进行归一化
        self.show_normalization_info = True  # 是否在标签中显示归一化信息
        
        # ==================== 文件保存设置 ====================
        self.save_formats = ['png', 'pdf']  # 保存格式
        self.save_bbox_inches = 'tight'     # 保存时的边界框设置
        
    def apply_global_style(self):
        """应用全局matplotlib样式设置"""
        plt.style.use('seaborn-v0_8')
        
        # 设置全局参数
        plt.rcParams.update({
            'font.size': self.font_size_base,
            'axes.titlesize': self.font_size_title,
            'axes.labelsize': self.font_size_label,
            'xtick.labelsize': self.font_size_tick,
            'ytick.labelsize': self.font_size_tick,
            'legend.fontsize': self.font_size_legend,
            'figure.figsize': self.figure_size,
            'figure.dpi': self.figure_dpi,
            'lines.linewidth': self.line_width,
            'grid.alpha': self.grid_alpha,
            'grid.linewidth': self.grid_line_width,
            'axes.grid': self.grid_enabled,
            'font.sans-serif': ['DejaVu Sans', 'Arial', 'SimHei'],
            'axes.unicode_minus': False
        })
    
    def create_figure(self):
        """创建标准化的图形对象"""
        return plt.figure(figsize=self.figure_size)
    
    def setup_axes(self, ax=None, xlabel='', ylabel='', title=''):
        """设置坐标轴样式"""
        if ax is None:
            ax = plt.gca()
        
        # 设置标签
        ax.set_xlabel(xlabel, fontsize=self.font_size_label)
        ax.set_ylabel(ylabel, fontsize=self.font_size_label)
        ax.set_title(title, fontsize=self.font_size_title)
        
        # 设置坐标轴范围
        ax.set_xlim(self.axis_limits['x_min'], self.axis_limits['x_max'])
        ax.set_ylim(self.axis_limits['y_min'], self.axis_limits['y_max'])
        
        # 设置网格
        if self.grid_enabled:
            ax.grid(True, alpha=self.grid_alpha, linewidth=self.grid_line_width)
        
        return ax
    
    def scatter_plot(self, x, y, color_key='e5', label='', ax=None):
        """标准化的散点图"""
        if ax is None:
            ax = plt.gca()
        
        color = self.colors.get(color_key, color_key)
        
        return ax.scatter(x, y, 
                         s=self.scatter_size,
                         alpha=self.scatter_alpha,
                         color=color,
                         edgecolors=self.scatter_edge_color,
                         linewidth=self.scatter_edge_width,
                         label=label)
    
    def fit_line_plot(self, x, y, color_key='fit_line', ax=None):
        """标准化的拟合线"""
        if ax is None:
            ax = plt.gca()
        
        color = self.colors.get(color_key, color_key)
        
        return ax.plot(x, y,
                      color=color,
                      linestyle=self.line_style_fit,
                      linewidth=self.line_width,
                      alpha=self.line_alpha)
    
    def add_annotation_box(self, text, position, box_color_key, ax=None):
        """添加标准化的注释框"""
        if ax is None:
            ax = plt.gca()
        
        box_color = self.annotation_colors.get(box_color_key, '#ffffff')
        
        return ax.text(position[0], position[1], text,
                      transform=ax.transAxes,
                      fontsize=self.annotation_box_style,
                      verticalalignment='top',
                      bbox=dict(boxstyle=self.annotation_box_style,
                               facecolor=box_color,
                               alpha=self.annotation_box_alpha))
    
    def setup_legend(self, ax=None):
        """设置标准化的图例"""
        if ax is None:
            ax = plt.gca()
        
        return ax.legend(fontsize=self.font_size_legend,
                        frameon=self.legend_frameon,
                        ncol=self.legend_ncol,
                        loc=self.legend_loc)
    
    def normalize_data(self, data, show_info=True):
        """数据归一化处理"""
        data_max = np.max(data)
        
        if data_max > self.normalize_threshold:
            normalized_data = data / data_max
            if show_info and self.show_normalization_info:
                info_suffix = f' (normalized by max={data_max:.3f})'
            else:
                info_suffix = ''
            return normalized_data, info_suffix
        else:
            return data, ''
    
    def save_figure(self, fig, filepath_without_ext):
        """保存图形到多种格式"""
        for fmt in self.save_formats:
            filepath = f"{filepath_without_ext}.{fmt}"
            fig.savefig(filepath,
                       format=fmt,
                       dpi=self.figure_dpi,
                       bbox_inches=self.save_bbox_inches)
        return [f"{filepath_without_ext}.{fmt}" for fmt in self.save_formats]

# 创建全局样式配置实例
PLOT_STYLE = PlotStyleConfig()

def get_style_config():
    """获取样式配置实例"""
    return PLOT_STYLE

def apply_global_style():
    """应用全局样式（便捷函数）"""
    PLOT_STYLE.apply_global_style()
