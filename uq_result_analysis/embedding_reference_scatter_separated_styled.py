#!/usr/bin/env python3
"""
使用统一样式配置的embedding reference散点图生成脚本
这是embedding_reference_scatter_separated.py的样式化版本
"""
import os, argparse, json
from pathlib import Path
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

# 导入统一样式配置
from plot_style_config import get_style_config, apply_global_style

def safe_float(v):
    try:
        if pd.isna(v):
            return np.nan
        return float(v)
    except Exception:
        return np.nan

def load_task_dataframe(task: str) -> pd.DataFrame:
    csv_path = Path(f'uq_result_analysis/data/UQ_result_{task}_with_reference.csv')
    if not csv_path.exists():
        raise FileNotFoundError(f"未找到数据文件: {csv_path}")
    return pd.read_csv(csv_path)

def extract_embedding_data_separated(df: pd.DataFrame) -> pd.DataFrame:
    """分别提取E5和Qwen的reference距离数据"""
    # 只保留embedding方法
    emb_df = df[df['uq_method'].str.contains('Embedding', case=False, na=False)].copy()
    if emb_df.empty:
        raise ValueError('CSV 中未找到 Embedding 方法行。')

    # 提取embedding模型类型和reference距离
    emb_df['embedding_model'] = emb_df['uq_method'].apply(
        lambda x: 'E5' if 'E5' in x else ('Qwen' if 'Qwen' in x else 'Unknown')
    )
    emb_df['reference_distance'] = emb_df['avg_distance_to_reference'].apply(safe_float)

    # 只保留有效的reference距离
    emb_df = emb_df.dropna(subset=['reference_distance'])

    return emb_df[['document_id', 'llm_model', 'embedding_model', 'reference_distance']]

def extract_uq_scores(df: pd.DataFrame, methods: list) -> pd.DataFrame:
    """提取指定UQ方法的分数"""
    subset = df[df['uq_method'].isin(methods)].copy()
    if subset.empty:
        return pd.DataFrame({'document_id': []})
    
    # 选择分数列
    def row_score(r):
        for k in ['uncertainty_score', 'uq_value']:
            v = r.get(k)
            if not pd.isna(v):
                return v
        return np.nan
    
    subset['uq_score'] = subset.apply(row_score, axis=1)
    wide = subset.pivot_table(index='document_id', columns='uq_method', 
                             values='uq_score', aggfunc='first')
    wide.reset_index(inplace=True)
    return wide

def linear_fit_with_pvalue(x: np.ndarray, y: np.ndarray) -> dict:
    """线性拟合，包含p-value计算"""
    mask = (~np.isnan(x)) & (~np.isnan(y))
    x_clean = x[mask]
    y_clean = y[mask]

    if len(x_clean) < 3:
        return {"coef": None, "intercept": None, "r2": None, "p_value": None, "n": len(x_clean)}

    # 使用numpy进行线性拟合
    coef = np.polyfit(x_clean, y_clean, 1)
    y_pred = np.polyval(coef, x_clean)

    # 计算R²
    ss_tot = np.sum((y_clean - np.mean(y_clean))**2)
    ss_res = np.sum((y_clean - y_pred)**2)
    r2 = 1 - (ss_res / ss_tot) if ss_tot > 0 else None

    # 计算p-value
    n = len(x_clean)
    slope = coef[0]

    # 计算标准误差
    mse = ss_res / (n - 2)
    x_mean = np.mean(x_clean)
    sxx = np.sum((x_clean - x_mean)**2)
    se_slope = np.sqrt(mse / sxx)

    # t统计量
    t_stat = slope / se_slope if se_slope > 0 else 0

    # 计算p-value (双尾检验)
    from scipy import stats
    p_value = 2 * (1 - stats.t.cdf(abs(t_stat), n - 2))

    return {
        "coef": float(coef[0]),
        "intercept": float(coef[1]),
        "r2": float(r2) if r2 is not None else None,
        "p_value": float(p_value),
        "n": int(len(x_clean))
    }

def create_styled_scatter_plot(x_data, y_data, method, llm_model, embedding_model, 
                              task, output_dir, style):
    """使用统一样式创建散点图"""
    
    # 数据归一化
    x_normalized, x_suffix = style.normalize_data(x_data)
    
    # 创建图形
    fig = style.create_figure()
    ax = plt.gca()
    
    # 选择颜色
    color_key = 'e5' if embedding_model == 'E5' else 'qwen'
    
    # 绘制散点图
    style.scatter_plot(x_normalized, y_data, 
                      color_key=color_key, 
                      label=f'{embedding_model} Data',
                      ax=ax)
    
    # 线性拟合
    fit_result = linear_fit_with_pvalue(x_normalized, y_data)
    
    if fit_result['coef'] is not None:
        # 绘制拟合线
        x_range = np.linspace(np.min(x_normalized), np.max(x_normalized), 100)
        y_fit = fit_result['coef'] * x_range + fit_result['intercept']
        style.fit_line_plot(x_range, y_fit, ax=ax)
        
        # 添加拟合信息注释
        coef, intercept, r2, p_value, n = (fit_result['coef'], fit_result['intercept'], 
                                          fit_result['r2'], fit_result['p_value'], fit_result['n'])
        
        # 公式
        if intercept >= 0:
            formula = f'y = {coef:.4f}x + {intercept:.4f}'
        else:
            formula = f'y = {coef:.4f}x - {abs(intercept):.4f}'
        
        # 添加注释框
        style.add_annotation_box(formula, (0.05, 0.95), 'formula', ax)
        style.add_annotation_box(f"R² = {r2:.4f}" if r2 is not None else "R² = -", 
                               (0.05, 0.88), 'r2', ax)
        style.add_annotation_box(f"p = {p_value:.4f}" if p_value is not None else "p = -", 
                               (0.05, 0.81), 'pvalue', ax)
        style.add_annotation_box(f'n = {n}', (0.05, 0.74), 'sample', ax)
    
    # 设置坐标轴
    style.setup_axes(ax,
                    xlabel=f'{method} Score{x_suffix}',
                    ylabel=f'Distance to Reference Text ({embedding_model})',
                    title=f'{task}: {llm_model} - {method} vs Reference Distance ({embedding_model})')
    
    plt.tight_layout()
    
    # 保存图形
    safe_llm_name = llm_model.replace(':', '_').replace('/', '_')
    filepath_base = output_dir / f'{safe_llm_name}_{method}_{embedding_model}'
    saved_files = style.save_figure(fig, str(filepath_base))
    
    plt.close()
    
    return fit_result, saved_files

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--task', default='topic_labeling', help='任务名称')
    parser.add_argument('--uq-methods', default='', help='逗号分隔的UQ方法名称，留空则使用所有非embedding方法')
    args = parser.parse_args()
    
    # 获取样式配置并应用全局样式
    style = get_style_config()
    apply_global_style()
    
    # 加载数据
    df = load_task_dataframe(args.task)
    
    # 提取embedding数据（分离E5和Qwen）
    emb_data = extract_embedding_data_separated(df)
    print(f"提取到 {len(emb_data)} 条embedding数据")
    print(f"E5数据: {len(emb_data[emb_data['embedding_model'] == 'E5'])} 条")
    print(f"Qwen数据: {len(emb_data[emb_data['embedding_model'] == 'Qwen'])} 条")
    
    # 获取UQ方法列表（排除embedding方法）
    if args.uq_methods.strip():
        uq_methods = [m.strip() for m in args.uq_methods.split(',') if m.strip()]
    else:
        all_methods = df['uq_method'].unique()
        uq_methods = [m for m in all_methods if 'Embedding' not in m]
    
    print(f"将分析以下UQ方法: {uq_methods}")
    
    # 提取UQ分数
    uq_scores = extract_uq_scores(df, uq_methods)
    
    # 创建输出目录
    fig_dir = Path('uq_result_analysis/figures')
    fig_dir.mkdir(parents=True, exist_ok=True)
    task_fig_dir = fig_dir / f'{args.task}_embedding_reference_styled'
    task_fig_dir.mkdir(parents=True, exist_ok=True)
    
    # 获取LLM模型列表
    llm_models = df['llm_model'].unique()
    print(f"发现LLM模型: {llm_models}")

    # 为每个LLM模型和UQ方法组合生成图表
    fit_results = {}
    all_saved_files = []

    for llm_model in llm_models:
        fit_results[llm_model] = {}
        llm_emb_data = emb_data[emb_data['llm_model'] == llm_model]

        for method in uq_methods:
            if method not in uq_scores.columns:
                print(f"跳过方法 {method}: 数据中不存在")
                continue

            # 合并数据
            merged = llm_emb_data.merge(uq_scores[['document_id', method]], on='document_id', how='inner')
            merged = merged.dropna(subset=[method, 'reference_distance'])

            if len(merged) < 2:
                print(f"跳过 {llm_model} - {method}: 有效数据点不足 ({len(merged)})")
                continue

            fit_results[llm_model][method] = {}

            # 分离E5和Qwen数据并创建图表
            for embedding_model in ['E5', 'Qwen']:
                model_data = merged[merged['embedding_model'] == embedding_model]
                
                if len(model_data) > 1:
                    x_data = model_data[method].values
                    y_data = model_data['reference_distance'].values
                    
                    fit_result, saved_files = create_styled_scatter_plot(
                        x_data, y_data, method, llm_model, embedding_model,
                        args.task, task_fig_dir, style
                    )
                    
                    fit_results[llm_model][method][embedding_model] = fit_result
                    all_saved_files.extend(saved_files)
                    
                    print(f'保存 {llm_model} - {method} {embedding_model}图像到 {saved_files}')
    
    # 保存拟合结果为CSV格式
    fit_csv = task_fig_dir / 'fitting_results.csv'
    rows = []
    for llm_model in fit_results:
        for method in fit_results[llm_model]:
            for emb_model in fit_results[llm_model][method]:
                data = fit_results[llm_model][method][emb_model]
                row = {
                    'task': args.task,
                    'llm_model': llm_model,
                    'uq_method': method,
                    'embedding_model': emb_model,
                    'coefficient': data['coef'],
                    'intercept': data['intercept'],
                    'r_squared': data['r2'],
                    'p_value': data['p_value'],
                    'sample_size': data['n']
                }
                rows.append(row)

    df_results = pd.DataFrame(rows)
    df_results.to_csv(fit_csv, index=False)

    print(f'拟合参数保存到 {fit_csv}')
    print(f'共生成 {len(all_saved_files)} 个图表文件，保存在 {task_fig_dir}')
    print(f'使用统一样式配置，可通过修改 plot_style_config.py 调整样式')

if __name__ == '__main__':
    main()
